<template>
	<page-container ref="pageContainer" :is-show-nav="false" bgColorPage="#E2ECEE">
		<custom-nav :is-back="false" bg-color="#E2ECEE" class="pt-40">
			<template #left>
				<image src="@/static/login/home-logo.png" mode="scaleToFill" class="w-150 h-80 mt-40 mr-20" />
			</template>
			<template #right>
				<view class="mt-40">
					<uv-icon name="plus-circle" color="#909399" size="32"></uv-icon>
				</view>
			</template>
			<template #nav-title>
				<uv-input
					placeholder="请输入服务器或者IP地址"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
					shape="circle"
					border="surround"
					:customStyle="{ backgroundColor: '#fff', height: '60rpx' }"
					class="mt-40"
				></uv-input>
			</template>
		</custom-nav>
		<view class="server-list-container px-16">
			<view class="server-list-item mt-16 p-16">
				<view class="flex items-center justify-between">
					<view class="flex items-center justify-between">
						<image src="@/static/server/server-logo.png" mode="scaleToFill" class="w-64 h-64 mr-20" />
						<view class="flex flex-col justify-between">
							<view class="flex items-center">
								<text class="text-30 font-bold">服务器列表</text>
								<text class="bg-#20a50a text-#fff px-8 py-2 rd-4 ml-10 text-24">在线2天</text>
							</view>
							<text class="text-24 mt-10 text-#999">IP：服务器列表</text>
						</view>
					</view>
					<view class="flex items-center justify-between rd-16 px-8 py-4" style="border: 3rpx solid rgba(0, 0, 0, 0.1);">
						<image src="@/static/server/arrow.png" mode="scaleToFill" class="w-64 h-64 mr-20" />
						<view class="flex flex-col items-center justify-between">
							<text class="text-20">CPU</text>
							<text class="text-20">100%</text>
						</view>
					</view>
				</view>
				<!-- 服务器监控指标网格 -->
				<view class="metrics-grid">
					<!-- 负载指标 - 左侧大卡片 -->
					<view class="load-card">
						<text class="load-title">负载</text>
						<text class="load-status">流畅</text>
						<text class="load-percentage">70%</text>
					</view>

					<!-- 右侧指标区域 -->
					<view class="right-metrics">
						<!-- CPU指标 -->
						<view class="metric-item cpu-item">
							<text class="metric-title">CPU</text>
							<text class="metric-subtitle">4核</text>
							<view class="arc-container">
								<view class="arc-progress cpu-arc"></view>
								<text class="arc-percentage">40%</text>
							</view>
						</view>

						<!-- 内存指标 -->
						<view class="metric-item memory-item">
							<text class="metric-title">内存</text>
							<text class="metric-subtitle">8GB</text>
							<view class="arc-container">
								<view class="arc-progress memory-arc"></view>
								<text class="arc-percentage">60%</text>
							</view>
						</view>
					</view>

					<!-- 磁盘指标 - 底部横条 -->
					<view class="disk-item">
						<view class="disk-header">
							<text class="disk-title">磁盘(/)</text>
							<text class="disk-total">40GB</text>
						</view>
						<view class="disk-progress-container">
							<view class="disk-progress-bar">
								<view class="disk-progress-fill"></view>
							</view>
							<text class="disk-percentage">48%</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref } from 'vue';
	import PageContainer from '@/components/pageContainer/index.vue';
	import CustomNav from '@/components/customNav/index.vue';
	import { pageContainer } from './useController';
	import { useConfigStore } from '@/store';
	import { $t } from '@/locale/index.js';
	const { phoneBrand, phoneModel, configList, harmonySslVerification } = useConfigStore().getReactiveState();

	const toggleMoreMenu = () => {
		console.log('toggleMoreMenu');
	};
</script>

<style lang="scss" scoped>
	.server-list-item {
		background: var(--bg-color);
		border-radius: 32rpx;
	}

	/* 监控指标网格布局 */
	.metrics-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		grid-template-rows: auto auto auto;
		gap: 16rpx;
		margin-top: 32rpx;
	}

	/* 负载卡片 - 左侧大卡片 */
	.load-card {
		grid-row: span 2;
		background: linear-gradient(135deg, #20a50a 0%, #f9d71c 100%);
		border-radius: 24rpx;
		padding: 32rpx 24rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: relative;
		min-height: 200rpx;
	}

	.load-title {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
	}

	.load-status {
		font-size: 24rpx;
		color: #20a50a;
		margin-bottom: 16rpx;
	}

	.load-percentage {
		font-size: 48rpx;
		font-weight: bold;
		color: #fff;
	}

	/* 右侧指标区域 */
	.right-metrics {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.metric-item {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 16rpx;
		padding: 20rpx 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		min-height: 92rpx;
	}

	.metric-title {
		font-size: 24rpx;
		color: #333;
		margin-bottom: 4rpx;
	}

	.metric-subtitle {
		font-size: 20rpx;
		color: #20a50a;
		margin-bottom: 12rpx;
	}

	/* 弧形进度条容器 */
	.arc-container {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.arc-progress {
		width: 60rpx;
		height: 30rpx;
		border-radius: 60rpx 60rpx 0 0;
		position: relative;
		overflow: hidden;
		background: #f0f0f0;

		&::before {
			content: '';
			position: absolute;
			width: 100%;
			height: 100%;
			border-radius: 60rpx 60rpx 0 0;
			background: #20a50a;
		}

		&.cpu-arc::before {
			transform: rotate(-108deg);
			transform-origin: center bottom;
		}

		&.memory-arc::before {
			transform: rotate(-72deg);
			transform-origin: center bottom;
		}
	}

	.arc-percentage {
		position: absolute;
		font-size: 20rpx;
		font-weight: bold;
		color: #20a50a;
		top: 32rpx;
	}

	/* 圆形进度条 */
	.progress-circle {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background: conic-gradient(#20a50a 0deg 252deg, #f0f0f0 252deg 360deg);
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;

		/* 负载指标的大圆形进度条 */
		.metric-item:first-child & {
			width: 160rpx;
			height: 160rpx;
		}

		&.cpu {
			background: conic-gradient(#20a50a 0deg 144deg, #f0f0f0 144deg 360deg);
		}

		&.memory {
			background: conic-gradient(#20a50a 0deg 216deg, #f0f0f0 216deg 360deg);
		}

		&::before {
			content: '';
			width: 56rpx;
			height: 56rpx;
			background: #fff;
			border-radius: 50%;
			position: absolute;
		}

		/* 负载指标的大圆形进度条内圈 */
		.metric-item:first-child &::before {
			width: 112rpx;
			height: 112rpx;
		}
	}

	.progress-text {
		font-size: 20rpx;
		font-weight: bold;
		color: #333;
		z-index: 1;

		/* 负载指标的大文字 */
		.metric-item:first-child & {
			font-size: 36rpx;
		}
	}

	/* 横向进度条 */
	.metric-progress {
		display: flex;
		align-items: center;
		gap: 16rpx;
		flex: 1;
		margin-left: 32rpx;
	}

	.progress-bar {
		flex: 1;
		height: 12rpx;
		background: #f0f0f0;
		border-radius: 6rpx;
		overflow: hidden;
	}

	.progress-fill {
		height: 100%;
		background: #20a50a;
		border-radius: 6rpx;
		transition: width 0.3s ease;
	}

	.progress-percentage {
		font-size: 24rpx;
		font-weight: bold;
		color: #20a50a;
		min-width: 60rpx;
		text-align: right;
	}
</style>
