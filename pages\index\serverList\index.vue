<template>
	<page-container ref="pageContainer" :is-show-nav="false" bgColorPage="#E2ECEE">
		<custom-nav :is-back="false" bg-color="#E2ECEE" class="pt-40">
			<template #left>
				<image src="@/static/login/home-logo.png" mode="scaleToFill" class="w-150 h-80 mt-40 mr-20" />
			</template>
			<template #right>
				<view class="mt-40">
					<uv-icon name="plus-circle" color="#909399" size="32"></uv-icon>
				</view>
			</template>
			<template #nav-title>
				<uv-input
					placeholder="请输入服务器或者IP地址"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
					shape="circle"
					border="surround"
					:customStyle="{ backgroundColor: '#fff', height: '60rpx' }"
					class="mt-40"
				></uv-input>
			</template>
		</custom-nav>
		<view class="server-list-container px-16">
			<view class="server-list-item mt-16 p-16">
				<view class="flex items-center justify-between">
					<view class="flex items-center justify-between">
						<image src="@/static/server/server-logo.png" mode="scaleToFill" class="w-64 h-64 mr-20" />
						<view class="flex flex-col justify-between">
							<view class="flex items-center">
								<text class="text-30 font-bold">服务器列表</text>
								<text class="bg-#20a50a text-#fff px-8 py-2 rd-4 ml-10 text-24">在线2天</text>
							</view>
							<text class="text-24 mt-10 text-#999">IP：服务器列表</text>
						</view>
					</view>
					<view class="flex items-center justify-between rd-16 px-8 py-4" style="border: 3rpx solid rgba(0, 0, 0, 0.1);">
						<image src="@/static/server/arrow.png" mode="scaleToFill" class="w-64 h-64 mr-20" />
						<view class="flex flex-col items-center justify-between">
							<text class="text-20">CPU</text>
							<text class="text-20">100%</text>
						</view>
					</view>
				</view>
				<!-- 服务器监控指标网格 -->
				<view class="metrics-grid">
					<!-- 负载指标 -->
					<view class="metric-item">
						<view class="metric-header">
							<text class="metric-title">负载</text>
							<text class="metric-status">流畅</text>
						</view>
						<view class="metric-chart">
							<view class="progress-circle">
								<text class="progress-text">70%</text>
							</view>
						</view>
					</view>

					<!-- CPU指标 -->
					<view class="metric-item">
						<view class="metric-header">
							<text class="metric-title">CPU</text>
							<text class="metric-cores">4核</text>
						</view>
						<view class="metric-chart">
							<view class="progress-circle cpu">
								<text class="progress-text">40%</text>
							</view>
						</view>
					</view>

					<!-- 内存指标 -->
					<view class="metric-item">
						<view class="metric-header">
							<text class="metric-title">内存</text>
							<text class="metric-total">8GB</text>
						</view>
						<view class="metric-chart">
							<view class="progress-circle memory">
								<text class="progress-text">60%</text>
							</view>
						</view>
					</view>

					<!-- 磁盘指标 -->
					<view class="metric-item metric-item-wide">
						<view class="metric-header">
							<text class="metric-title">磁盘(/)</text>
							<text class="metric-total">40GB</text>
						</view>
						<view class="metric-progress">
							<view class="progress-bar">
								<view class="progress-fill" style="width: 48%"></view>
							</view>
							<text class="progress-percentage">48%</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref } from 'vue';
	import PageContainer from '@/components/pageContainer/index.vue';
	import CustomNav from '@/components/customNav/index.vue';
	import { pageContainer } from './useController';
	import { useConfigStore } from '@/store';
	import { $t } from '@/locale/index.js';
	const { phoneBrand, phoneModel, configList, harmonySslVerification } = useConfigStore().getReactiveState();

	const toggleMoreMenu = () => {
		console.log('toggleMoreMenu');
	};
</script>

<style lang="scss" scoped>
	.server-list-item {
		background: var(--bg-color);
		border-radius: 32rpx;
	}

	/* 监控指标网格布局 */
	.metrics-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 24rpx;
		margin-top: 32rpx;
	}

	.metric-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 24rpx 16rpx;
		background: rgba(255, 255, 255, 0.8);
		border-radius: 16rpx;
		border: 2rpx solid rgba(0, 0, 0, 0.05);

		&.metric-item-wide {
			grid-column: span 3;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			padding: 24rpx 32rpx;
		}
	}

	.metric-header {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 16rpx;

		.metric-item-wide & {
			align-items: flex-start;
			margin-bottom: 0;
		}
	}

	.metric-title {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 8rpx;
	}

	.metric-status,
	.metric-cores,
	.metric-total {
		font-size: 20rpx;
		color: #999;
	}

	.metric-chart {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 圆形进度条 */
	.progress-circle {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background: conic-gradient(#20a50a 0deg 252deg, #f0f0f0 252deg 360deg);
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;

		&.cpu {
			background: conic-gradient(#20a50a 0deg 144deg, #f0f0f0 144deg 360deg);
		}

		&.memory {
			background: conic-gradient(#20a50a 0deg 216deg, #f0f0f0 216deg 360deg);
		}

		&::before {
			content: '';
			width: 56rpx;
			height: 56rpx;
			background: #fff;
			border-radius: 50%;
			position: absolute;
		}
	}

	.progress-text {
		font-size: 20rpx;
		font-weight: bold;
		color: #333;
		z-index: 1;
	}

	/* 横向进度条 */
	.metric-progress {
		display: flex;
		align-items: center;
		gap: 16rpx;
		flex: 1;
		margin-left: 32rpx;
	}

	.progress-bar {
		flex: 1;
		height: 12rpx;
		background: #f0f0f0;
		border-radius: 6rpx;
		overflow: hidden;
	}

	.progress-fill {
		height: 100%;
		background: #20a50a;
		border-radius: 6rpx;
		transition: width 0.3s ease;
	}

	.progress-percentage {
		font-size: 24rpx;
		font-weight: bold;
		color: #20a50a;
		min-width: 60rpx;
		text-align: right;
	}
</style>
