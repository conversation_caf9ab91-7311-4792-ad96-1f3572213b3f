<template>
	<page-container ref="pageContainer" :is-show-nav="false" bgColorPage="#E2ECEE">
		<custom-nav :is-back="false" bg-color="#E2ECEE" class="pt-40">
			<template #left>
				<image src="@/static/login/home-logo.png" mode="scaleToFill" class="w-150 h-80 mt-40 mr-20" />
			</template>
			<template #right>
				<view class="mt-40">
					<uv-icon name="plus-circle" color="#909399" size="32"></uv-icon>
				</view>
			</template>
			<template #nav-title>
				<uv-input
					placeholder="请输入服务器或者IP地址"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
					shape="circle"
					border="surround"
					:customStyle="{ backgroundColor: '#fff', height: '60rpx' }"
					class="mt-40"
				></uv-input>
			</template>
		</custom-nav>
		<view class="server-list-container px-16">
			<view class="server-list-item mt-16 p-16">
				<view class="flex items-center justify-between">
					<view class="flex items-center justify-between">
						<image src="@/static/server/server-logo.png" mode="scaleToFill" class="w-64 h-64 mr-20" />
						<view class="flex flex-col justify-between">
							<view class="flex items-center">
								<text class="text-30 font-bold">服务器列表</text>
								<text class="bg-#20a50a text-#fff px-8 py-2 rd-4 ml-10 text-24">在线2天</text>
							</view>
							<text class="text-24 mt-10 text-#999">IP：服务器列表</text>
						</view>
					</view>
					<view class="flex items-center justify-between rd-16 px-8 py-4" style="border: 3rpx solid rgba(0, 0, 0, 0.1);">
						<image src="@/static/server/arrow.png" mode="scaleToFill" class="w-64 h-64 mr-20" />
						<view class="flex flex-col items-center justify-between">
							<text class="text-20">CPU</text>
							<text class="text-20">100%</text>
						</view>
					</view>
				</view>
				<view class="grid"></view>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref } from 'vue';
	import PageContainer from '@/components/pageContainer/index.vue';
	import CustomNav from '@/components/customNav/index.vue';
	import { pageContainer } from './useController';
	import { useConfigStore } from '@/store';
	import { $t } from '@/locale/index.js';
	const { phoneBrand, phoneModel, configList, harmonySslVerification } = useConfigStore().getReactiveState();

	const toggleMoreMenu = () => {
		console.log('toggleMoreMenu');
	};
</script>

<style lang="scss" scoped>
	.server-list-item {
		background: var(--bg-color);
		border-radius: 32rpx;
	}
</style>
